<?php namespace TierPricingTable\Integrations\Plugins;

use TierPricingTable\PriceManager;
use WC_Product;

class WooCommerceProductOptions extends PluginIntegrationAbstract {
	
	/**
	 * Add extra product options costs to tiered price in cart (for calculation only).
	 *
	 * @param  float  $price  The tiered price per item
	 * @param  array  $cart_item
	 *
	 * @return float
	 */
	public function addOptionsPriceToTieredPrice( $price, $cart_item ) {

		if ( ! isset( $cart_item['wpo_options'] ) || false == $price ) {
			return $price;
		}

		return $price + $this->calculateOptionsPrice( $cart_item, $price );
	}

	/**
	 * Handle display prices - show base prices only for display contexts
	 *
	 * @param  float  $price  The tiered price per item
	 * @param  array  $cart_item
	 *
	 * @return float
	 */
	public function addOptionsPriceToDisplayPrice( $price, $cart_item ) {

		if ( ! isset( $cart_item['wpo_options'] ) || false == $price ) {
			return $price;
		}

		// For display contexts (mini cart, cart page), show base prices only
		// This ensures the price breakdown shows correct base prices
		return $price;
	}

	/**
	 * DO NOT add options price to old price - keep it as base product price
	 *
	 * @param  float  $price  The old/regular price per item
	 * @param  array  $cart_item
	 *
	 * @return float
	 */
	public function keepOldPriceWithoutOptions( $price, $cart_item ) {
		// Return the old price without adding options
		// This ensures the mini cart shows the correct base prices
		return $price;
	}

	/**
	 * Calculate the total options price per item
	 *
	 * @param  array  $cart_item
	 * @param  float  $base_price
	 *
	 * @return float
	 */
	private function calculateOptionsPrice( $cart_item, $base_price ) {

		// Use WooCommerce Product Options' own calculation method
		if ( ! class_exists( 'Barn2\Plugin\WC_Product_Options\Util\Price' ) ) {
			return 0;
		}

		$product = $cart_item['data'];
		$quantity = $cart_item['quantity'];
		$options_total_per_item = 0;

		foreach ( $cart_item['wpo_options'] as $option_data ) {
			if ( ! isset( $option_data['choice_data'] ) ) {
				continue;
			}

			foreach ( $option_data['choice_data'] as $choice_data ) {
				if ( ! isset( $choice_data['pricing'] ) ) {
					continue;
				}

				// Use WPO's own calculation method to get the per-item option cost
				$option_price_per_item = \Barn2\Plugin\WC_Product_Options\Util\Price::calculate_option_cart_price(
					$choice_data['pricing'],
					$product,
					$quantity,
					$base_price
				);

				$options_total_per_item += $option_price_per_item;
			}
		}

		return $options_total_per_item;
	}
	
	/**
	 * Render compatibility script for frontend price updates
	 */
	public function addCompatibilityScript() {
		?>
		<script>
			(function ($) {
				// Update product options when tiered price changes
				$(document).on('tiered_price_update', function (event, data) {
					// Trigger recalculation of product options pricing
					if (typeof window.wpo_price_calculator !== 'undefined') {
						window.wpo_price_calculator.update_price();
					}
					
					// Update any product option price displays
					$('.wpo-option-price').each(function() {
						var $this = $(this);
						var basePrice = parseFloat($this.data('base-price') || 0);
						var newPrice = data.price;
						
						// Update the base price for percentage calculations
						$this.data('product-price', newPrice);
					});
				});
			})(jQuery);
		</script>
		<?php
	}
	
	public function getTitle(): string {
		return __( 'WooCommerce Product Options (by Barn2)', 'tier-pricing-table' );
	}
	
	public function getDescription(): string {
		return __( 'Make tiered pricing work properly with WooCommerce Product Options plugin.', 'tier-pricing-table' );
	}
	
	public function getSlug(): string {
		return 'woocommerce-product-options';
	}
	
	public function getAuthorURL(): string {
		return 'https://barn2.com/wordpress-plugins/woocommerce-product-options/';
	}
	
	public function getIconURL(): string {
		return $this->getContainer()->getFileManager()->locateAsset( 'admin/integrations/placeholder.png' );
	}
	
	public function run() {
		include_once ABSPATH . 'wp-admin/includes/plugin.php';

		if ( is_plugin_active( 'woocommerce-product-options/woocommerce-product-options.php' ) ) {

			add_action( 'wp_footer', array( $this, 'addCompatibilityScript' ) );

			// Add product options price to tiered pricing calculations (for cart totals)
			add_filter( 'tiered_pricing_table/cart/product_cart_price', array( $this, 'addOptionsPriceToTieredPrice' ), 10, 2 );

			// Handle display separately - don't add options to display prices
			add_filter( 'tiered_pricing_table/cart/product_cart_price/item', array( $this, 'addOptionsPriceToDisplayPrice' ), 10, 2 );

			// DO NOT add options to old price - keep it as base product price for proper mini cart display
			add_filter( 'tiered_pricing_table/cart/product_cart_old_price', array( $this, 'keepOldPriceWithoutOptions' ), 10, 2 );

			// Apply the same logic as mini cart to WooCommerce Blocks (higher priority than TPT's own filter)
			add_filter( 'woocommerce_cart_item_price', array( $this, 'modifyCartItemPriceForBlocks' ), 1000, 3 );

			// Hook into Store API to modify price data for WooCommerce Blocks
			add_filter( 'woocommerce_store_api_cart_item_data', array( $this, 'modifyStoreApiCartItemData' ), 10, 3 );

			// Prevent WPO from overriding tiered pricing in cart
			add_filter( 'wc_product_options_enable_price_calculation', array( $this, 'disableWpoCartCalculation' ), 10, 3 );

			// Handle AJAX price requests for product options
			add_filter( 'wc_product_options_ajax_get_product_price',
				function ( $price, $qty, WC_Product $product ) {

					$pricingRule = PriceManager::getPricingRule( $product->get_id() );
					$newPrice    = $pricingRule->getTierPrice( $qty, false );

					if ( $newPrice ) {
						return $newPrice;
					}

					return $price;
				}, 10, 3 );
		}
	}

	/**
	 * Disable WPO's own price calculation in cart when tiered pricing is active
	 *
	 * @param bool $enable
	 * @param WC_Product $product
	 * @param array $cart_item
	 * @return bool
	 */
	public function disableWpoCartCalculation( $enable, $product, $cart_item ) {
		// Check if this product has tiered pricing rules
		$pricingRule = PriceManager::getPricingRule( $product->get_id() );

		if ( ! empty( $pricingRule->getRules() ) ) {
			// Disable WPO's price calculation, we'll handle it in our integration
			return false;
		}

		return $enable;
	}


	
	public function getIntegrationCategory(): string {
		return 'product_addons';
	}

	/**
	 * Modify cart item price display to show only tiered price (without addons)
	 * This ensures cart page shows base tiered prices like the mini cart does
	 */
	public function modifyCartItemPriceForBlocks( $price, $cart_item, $cart_item_key ) {
		// Only apply to products with WPO options
		if ( ! isset( $cart_item['wpo_options'] ) ) {
			return $price;
		}

		// Only apply on cart page or AJAX requests (not checkout or other contexts)
		if ( ! is_cart() && ! wp_doing_ajax() ) {
			return $price;
		}

		$product = $cart_item['data'];
		$pricingRule = PriceManager::getPricingRule( $product->get_id() );

		// Get the total quantity for this product across all cart items
		$total_quantity = 0;
		if ( WC()->cart ) {
			foreach ( WC()->cart->get_cart() as $item ) {
				if ( $item['data']->get_id() === $product->get_id() ) {
					$total_quantity += $item['quantity'];
				}
			}
		}

		$tiered_price = $pricingRule->getTierPrice( $total_quantity, false );

		if ( $tiered_price ) {
			// Return ONLY the base tiered price (same as mini cart logic)
			return wc_price( $tiered_price );
		}

		return $price;
	}

	/**
	 * Modify Store API cart item data to show correct prices in WooCommerce Blocks
	 */
	public function modifyStoreApiCartItemData( $cart_item_data, $cart_item, $cart_item_key ) {
		// Only apply to products with WPO options
		if ( ! isset( $cart_item['wpo_options'] ) ) {
			return $cart_item_data;
		}

		$product = $cart_item['data'];
		$pricingRule = PriceManager::getPricingRule( $product->get_id() );

		// Get the total quantity for this product across all cart items
		$total_quantity = 0;
		if ( WC()->cart ) {
			foreach ( WC()->cart->get_cart() as $item ) {
				if ( $item['data']->get_id() === $product->get_id() ) {
					$total_quantity += $item['quantity'];
				}
			}
		}

		$tiered_price = $pricingRule->getTierPrice( $total_quantity, false );

		if ( $tiered_price && isset( $cart_item_data['prices'] ) ) {
			// Override the price in the Store API response to show tiered price only
			// Store API uses cents, so multiply by 100
			$tiered_price_cents = intval( $tiered_price * 100 );

			$cart_item_data['prices']['price'] = $tiered_price_cents;
			$cart_item_data['prices']['regular_price'] = $tiered_price_cents;
			$cart_item_data['prices']['sale_price'] = $tiered_price_cents;
		}

		return $cart_item_data;
	}
}
