<?php namespace TierPricingTable\Services;

/*
 * Class TieredPricingCartService
 *
 * Service modifies product's price in the cart based on quantity and tiered pricing rules
 *
 * @package TierPricingTable\Services
 */

use TierPricingTable\CalculationLogic;
use TierPricingTable\Core\ServiceContainerTrait;
use TierPricingTable\PriceManager;
use TierPricingTable\Settings\Sections\GeneralSection\Subsections\CartOptionsSubsection;
use WC_Product;
use WC_Cart;

class TieredPricingCartService {
	
	use ServiceContainerTrait;
	
	protected $originalPrices = [];
	
	public function __construct() {
		add_action( 'woocommerce_before_calculate_totals', array( $this, 'calculateTieredPricingInCart' ), 99999, 3 );
		add_action( 'woocommerce_before_mini_cart_contents', array( $this, 'triggerMiniCartUpdate' ), 9999, 3 );
		add_filter( 'woocommerce_cart_item_price', array( $this, 'calculateCartItemPrice' ), 999, 2 );
		add_filter( 'woocommerce_cart_item_subtotal', array( $this, 'modifyCartItemSubtotal' ), 999, 3 );

		// Force mini cart refresh when cart contents change
		add_action( 'woocommerce_add_to_cart', array( $this, 'forceMiniCartRefresh' ), 10 );
		add_action( 'woocommerce_cart_item_removed', array( $this, 'forceMiniCartRefresh' ), 10 );
		add_action( 'woocommerce_after_cart_item_quantity_update', array( $this, 'forceMiniCartRefresh' ), 10 );

		// Initialize test mode for debugging
		$this->initTestMode();

		// Include test refresh functionality
		$this->includeTestRefresh();
	}
	
	public function modifyCartItemSubtotal( $subtotal, $cartItem, $cartItemKey ) {
		
		$newPrice = $this->getCartItemPrice( $cartItem, $cartItemKey );
		
		if ( false === $newPrice ) {
			return $subtotal;
		}
		
		$recalculateCartItemSubtotal = apply_filters( 'tiered_pricing_table/cart/recalculate_cart_item_subtotal', true,
			$cartItem, $cartItemKey, $subtotal );
		
		if ( ! $recalculateCartItemSubtotal ) {
			return $subtotal;
		}
		
		$considerSalePriceAsDiscount = $this->getContainer()->getSettings()->get( 'consider_sale_price_as_discount_in_cart',
				'no' ) === 'yes';
		
		$considerSalePriceAsDiscount = apply_filters( 'tiered_pricing_table/cart/subtotal/consider_sale_price_as_discount',
			$considerSalePriceAsDiscount, $cartItem, $cartItemKey );
		
		// Reset product instance because prices is already modified in the "woocommerce_before_calculate_totals" hook.
		// We will not be able to get the original price
		$product = wc_get_product( $cartItem['data']->get_id() );
		
		if ( $product->is_taxable() ) {
			
			if ( wc()->cart->display_prices_including_tax() ) {
				
				$originalCartItemPrice = wc_get_price_including_tax( $product, array(
					'qty'   => $cartItem['quantity'],
					'price' => $considerSalePriceAsDiscount ? $product->get_regular_price() : $product->get_price(),
				) );
				
				$newCartItemPrice = wc_get_price_including_tax( $product, array(
					'qty'   => $cartItem['quantity'],
					'price' => $newPrice,
				) );
				
				$originalProductSubtotal = wc_price( $originalCartItemPrice );
				$newProductSubtotal      = wc_price( $newCartItemPrice );
				
				if ( $originalCartItemPrice !== $newCartItemPrice && CartOptionsSubsection::showSubtotalInCartAsDiscount() ) {
					$newProductSubtotal = '<del>' . $originalProductSubtotal . '</del> <ins>' . $newProductSubtotal . '</ins>';
				}
				
				if ( ! wc_prices_include_tax() && wc()->cart->get_subtotal_tax() > 0 ) {
					$newProductSubtotal .= ' <small class="tax_label">' . WC()->countries->inc_tax_or_vat() . '</small>';
				}
			} else {
				$originalCartItemPrice = wc_get_price_excluding_tax( $product, array(
					'qty'   => $cartItem['quantity'],
					'price' => $considerSalePriceAsDiscount ? $product->get_regular_price() : $product->get_price(),
				) );
				
				$newCartItemPrice = wc_get_price_excluding_tax( $product, array(
					'qty'   => $cartItem['quantity'],
					'price' => $newPrice,
				) );
				
				$originalProductSubtotal = wc_price( $originalCartItemPrice );
				$newProductSubtotal      = wc_price( $newCartItemPrice );
				
				if ( $originalCartItemPrice !== $newCartItemPrice && CartOptionsSubsection::showSubtotalInCartAsDiscount() ) {
					$newProductSubtotal = '<del>' . $originalProductSubtotal . '</del> <ins>' . $newProductSubtotal . '</ins>';
				}
				
				if ( wc_prices_include_tax() && wc()->cart->get_subtotal_tax() > 0 ) {
					$newProductSubtotal .= ' <small class="tax_label">' . WC()->countries->ex_tax_or_vat() . '</small>';
				}
			}
		} else {
			$_originalItemPrice    = $considerSalePriceAsDiscount ? $product->get_regular_price() : $product->get_price();
			$originalCartItemPrice = ( float ) $_originalItemPrice * ( float ) $cartItem['quantity'];
			$newCartItemPrice      = ( float ) $newPrice * ( float ) $cartItem['quantity'];
			
			$originalProductSubtotal = wc_price( $originalCartItemPrice );
			$newProductSubtotal      = wc_price( $newCartItemPrice );
			
			if ( $originalProductSubtotal !== $newProductSubtotal && CartOptionsSubsection::showSubtotalInCartAsDiscount() ) {
				$newProductSubtotal = '<del>' . $originalProductSubtotal . '</del> <ins>' . $newProductSubtotal . '</ins>';
			}
		}
		
		return $newProductSubtotal;
	}
	
	public function getCartItemPrice( $cartItem, $cartItemKey, $cart = null ) {
		
		$cart = $cart instanceof WC_Cart ? $cart : wc()->cart;
		
		if ( ! ( $cart instanceof WC_Cart ) ) {
			return false;
		}
		
		$calculateTieredPriceForItem = apply_filters( 'tiered_pricing_table/cart/need_price_recalculation', true,
			$cartItem, $cart );
		
		if ( ! $calculateTieredPriceForItem ) {
			return false;
		}
		
		if ( empty( $cartItem['data'] ) || ! ( $cartItem['data'] instanceof WC_Product ) ) {
			return false;
		}
		
		$pricingRule = PriceManager::getPricingRule( $cartItem['data']->get_id() );
		
		$totalQuantity = $this->getTotalProductCount( $cartItem );
		$newPrice      = $pricingRule->getTierPrice( $totalQuantity, false, 'cart' );
		
		return apply_filters( 'tiered_pricing_table/cart/product_cart_price', $newPrice, $cartItem, $cartItemKey,
			$totalQuantity );
	}
	
	/**
	 * Calculate price by quantity rules
	 *
	 * @param  WC_Cart  $cart
	 */
	public function calculateTieredPricingInCart( WC_Cart $cart ) {
		
		if ( ! empty( $cart->get_cart_contents() ) ) {
			
			foreach ( $cart->get_cart_contents() as $key => $cartItem ) {
				
				$newPrice = $this->getCartItemPrice( $cartItem, $key, $cart );
				
				if ( false !== $newPrice ) {
					
					if ( ! isset( $this->originalPrices[ $key ] ) ) {
						$this->originalPrices[ $key ] = $cartItem['data']->get_price();
					}
					
					$cartItem['data']->set_price( $newPrice );
					$cartItem['data']->add_meta_data( 'tiered_pricing_cart_price_calculated', 'yes' );
				} else {
					
					if ( isset( $this->originalPrices[ $key ] ) ) {
						$cartItem['data']->set_price( $this->originalPrices[ $key ] );
					}
				}
				
				// Update tiered pricing data
				$cart->cart_contents[ $key ]['tiered_pricing_data'] = array(
					'total_item_quantity' => $this->getTotalProductCount( $cartItem ),
				);
			}
		}
	}
	
	/**
	 * Calculate price in mini cart
	 *
	 * @param  string  $price
	 * @param  array  $cartItem
	 *
	 * @return string
	 */
	public function calculateCartItemPrice( $price, $cartItem ) {
		
		$calculateTieredPriceForItem = apply_filters( 'tiered_pricing_table/cart/need_price_recalculation/item', true,
			$cartItem );
		
		if ( $cartItem['data'] instanceof WC_Product && $calculateTieredPriceForItem ) {
			
			$product = wc_get_product( $cartItem['data']->get_id() );
			
			$pricingRule = PriceManager::getPricingRule( $cartItem['data']->get_id() );
			
			$newPrice = $pricingRule->getTierPrice( $this->getTotalProductCount( $cartItem ), true, 'cart' );
			$newPrice = apply_filters( 'tiered_pricing_table/cart/product_cart_price/item', $newPrice, $cartItem );
			
			if ( false !== $newPrice ) {
				
				if ( CalculationLogic::showTieredPriceInCartAsDiscount() ) {
					$considerSalePriceAsDiscount = $this->getContainer()->getSettings()->get( 'consider_sale_price_as_discount_in_cart',
							'no' ) === 'yes';
					
					$considerSalePriceAsDiscount = apply_filters( 'tiered_pricing_table/cart/item/consider_sale_price_as_discount',
						$considerSalePriceAsDiscount, $cartItem );
					
					$_oldPrice = $considerSalePriceAsDiscount ? $product->get_regular_price() : $product->get_price();
					
					$oldPrice = PriceManager::getPriceToDisplay( $_oldPrice, $product, 'cart' );
					
					$oldPrice = apply_filters( 'tiered_pricing_table/cart/product_cart_old_price', $oldPrice,
						$cartItem );
					
					return '<del> ' . wc_price( $oldPrice ) . ' </del> <ins> ' . wc_price( $newPrice ) . ' </ins>';
				}
				
				return wc_price( $newPrice );
			}
		}
		
		return $price;
	}
	
	public function triggerMiniCartUpdate() {
		$cart = wc()->cart;
		$cart->calculate_totals();

		// Force mini cart fragment refresh
		if ( defined( 'DOING_AJAX' ) && DOING_AJAX ) {
			// Clear mini cart cache to force refresh
			WC()->cart->set_session();
		}
	}

	/**
	 * Force mini cart refresh by clearing fragments cache
	 */
	public function forceMiniCartRefresh() {
		// Clear mini cart fragments cache
		if ( function_exists( 'wc_delete_shop_order_transients' ) ) {
			delete_transient( 'wc_cart_hash_' . md5( get_current_user_id() ) );
		}

		// Clear any WooCommerce fragments
		if ( isset( $_COOKIE['woocommerce_cart_hash'] ) ) {
			unset( $_COOKIE['woocommerce_cart_hash'] );
		}
	}

	/**
	 * Force recalculation of all cart item prices
	 * This can be useful when tier pricing logic changes
	 */
	public function forceCartRecalculation() {
		// Clear any cached pricing data
		$this->originalPrices = [];

		// Force recalculation
		if ( wc()->cart ) {
			wc()->cart->calculate_totals();
		}

		// Also force mini cart refresh
		$this->forceMiniCartRefresh();
	}

	/**
	 * Add a simple test function to verify the fix
	 * This can be called via URL parameter for testing
	 */
	public function initTestMode() {
		// Only for admins and when test parameter is present
		if ( current_user_can( 'manage_options' ) && isset( $_GET['tpt_test_cart'] ) ) {
			add_action( 'wp_footer', function() {
				echo '<div style="position: fixed; bottom: 10px; right: 10px; background: #fff; border: 1px solid #ccc; padding: 10px; z-index: 9999;">';
				echo '<strong>TPT Cart Test Mode</strong><br>';

				if ( wc()->cart && ! wc()->cart->is_empty() ) {
					echo 'Cart items:<br>';
					foreach ( wc()->cart->get_cart() as $cart_item_key => $cart_item ) {
						$total_qty = $this->getTotalProductCount( $cart_item );
						echo sprintf( 'Product ID: %d, Qty: %d, Total Qty: %d<br>',
							$cart_item['product_id'],
							$cart_item['quantity'],
							$total_qty
						);
					}
				} else {
					echo 'Cart is empty';
				}

				echo '<br><button onclick="location.reload()">Refresh Page</button>';
				echo '<br><button onclick="jQuery(document.body).trigger(\'wc_fragment_refresh\')">Refresh Cart</button>';
				echo '</div>';
			});
		}

		// Add refresh cart functionality for testing
		if ( current_user_can( 'manage_options' ) && isset( $_GET['tpt_refresh_cart'] ) ) {
			add_action( 'wp_footer', function() {
				?>
				<script type="text/javascript">
				jQuery(document).ready(function($) {
					// Force refresh cart fragments
					$(document.body).trigger('wc_fragment_refresh');

					// Also try to refresh mini cart specifically
					if (typeof wc_cart_fragments_params !== 'undefined') {
						$.ajax({
							url: wc_cart_fragments_params.wc_ajax_url.toString().replace('%%endpoint%%', 'get_refreshed_fragments'),
							type: 'POST',
							success: function(data) {
								if (data && data.fragments) {
									$.each(data.fragments, function(key, value) {
										$(key).replaceWith(value);
									});
								}
							}
						});
					}
				});
				</script>
				<?php
			});
		}
	}

	/**
	 * Include test refresh functionality
	 */
	private function includeTestRefresh() {
		$test_file = dirname( __DIR__, 2 ) . '/test-cart-refresh.php';
		if ( file_exists( $test_file ) ) {
			include_once $test_file;
		}
	}
	
	/**
	 * Get total product count depends on user's settings
	 *
	 * @param  ?array  $cartItem
	 *
	 * @return int
	 */
	public function getTotalProductCount( ?array $cartItem ): int {

		if ( CalculationLogic::considerProductVariationAsOneProduct() ) {
			// Sum quantities for all variations of the same parent product
			$count = 0;

			foreach ( wc()->cart->cart_contents as $cart_content ) {
				if ( $cart_content['product_id'] == $cartItem['product_id'] ) {
					$count += $cart_content['quantity'];
				}
			}
		} else {
			// Sum quantities for identical products (same product_id and variation_id)
			$count = 0;
			$current_product_id = $cartItem['product_id'];
			$current_variation_id = isset( $cartItem['variation_id'] ) ? $cartItem['variation_id'] : 0;

			foreach ( wc()->cart->cart_contents as $cart_content ) {
				$cart_product_id = $cart_content['product_id'];
				$cart_variation_id = isset( $cart_content['variation_id'] ) ? $cart_content['variation_id'] : 0;

				// Check if this is the same exact product (same product_id and variation_id)
				if ( $cart_product_id == $current_product_id && $cart_variation_id == $current_variation_id ) {
					$count += $cart_content['quantity'];
				}
			}
		}

		return (int) apply_filters( 'tiered_pricing_table/cart/total_product_count', $count, $cartItem );
	}
}
